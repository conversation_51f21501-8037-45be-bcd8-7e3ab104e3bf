const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './.env' });

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:3004', 'http://localhost:3000', 'http://localhost:19006'],
  credentials: true
}));
app.use(express.json());

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API health check
app.get('/api/v1/health', async (req, res) => {
  try {
    // Test Supabase connection
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    res.json({ 
      status: 'healthy',
      database: error ? 'disconnected' : 'connected',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      supabase: {
        url: process.env.SUPABASE_URL ? 'configured' : 'missing',
        connection: error ? 'failed' : 'success'
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Test authentication endpoint
app.post('/api/v1/auth/test', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }

    // First, test if users table exists
    const { data: tableData, error: tableError } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (tableError) {
      console.log('Users table error:', tableError);
      return res.json({
        success: true,
        message: 'Database connected but users table not accessible',
        error: tableError.message,
        tableExists: false
      });
    }

    // Test user lookup with Supabase - use correct column names
    const { data, error } = await supabase
      .from('users')
      .select('id, email, first_name, last_name, role, has_completed_onboarding')
      .eq('email', email)
      .limit(1);

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.log('User lookup error:', error);
      return res.json({
        success: true,
        message: 'Database connected but query failed',
        error: error.message,
        tableExists: true
      });
    }

    res.json({
      success: true,
      user: data && data.length > 0 ? data[0] : null,
      message: data && data.length > 0 ? 'User found' : 'User not found',
      tableExists: true
    });

  } catch (error) {
    console.error('Auth test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// AI onboarding test endpoint
app.post('/api/v1/ai/test', async (req, res) => {
  try {
    const { message, userRole } = req.body;

    // Mock AI response for testing
    const mockResponse = {
      success: true,
      response: `مرحباً! أنا مساعد Freela Syria الذكي. ${userRole === 'EXPERT' ? 'سأساعدك في إنشاء ملفك الشخصي كخبير.' : 'سأساعدك في العثور على الخدمات المناسبة لك.'}`,
      sessionId: 'test-session-' + Date.now(),
      nextStep: 'profile_setup',
      extractedData: {
        userRole: userRole || 'CLIENT',
        language: 'ar'
      }
    };

    res.json(mockResponse);

  } catch (error) {
    console.error('AI test error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API documentation
app.get('/api/v1/docs', (req, res) => {
  res.json({
    title: 'Freela Syria API - Test Server',
    version: '1.0.0',
    endpoints: {
      'GET /health': 'Basic health check',
      'GET /api/v1/health': 'API health check with database status',
      'POST /api/v1/auth/test': 'Test authentication with email',
      'POST /api/v1/ai/test': 'Test AI onboarding system',
      'GET /api/v1/docs': 'This documentation'
    },
    database: {
      type: 'Supabase',
      status: 'Using Supabase client instead of direct PostgreSQL'
    }
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message,
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Test API Server running on port ${PORT}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/api/v1/docs`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/health`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 CORS Origins: http://localhost:3004, http://localhost:3000`);
});

module.exports = app;
