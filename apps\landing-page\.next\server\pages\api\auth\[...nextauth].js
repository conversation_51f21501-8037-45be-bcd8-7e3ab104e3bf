"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/auth/[...nextauth]";
exports.ids = ["pages/api/auth/[...nextauth]"];
exports.modules = {

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/providers/google":
/*!*********************************************!*\
  !*** external "next-auth/providers/google" ***!
  \*********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/google");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/../../node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/../../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\auth\\[...nextauth].ts */ \"(api)/./src/pages/api/auth/[...nextauth].ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/auth/[...nextauth]\",\n        pathname: \"/api/auth/[...nextauth]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_auth_nextauth_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/pages/api/auth/[...nextauth].ts":
/*!*********************************************!*\
  !*** ./src/pages/api/auth/[...nextauth].ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"next-auth/providers/google\");\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// import { PrismaAdapter } from '@next-auth/prisma-adapter';\n// import { prisma } from '@freela/database';\n// import { Prisma } from '@prisma/client';\nconst authOptions = {\n    // adapter: PrismaAdapter(prisma), // Temporarily disabled for testing\n    providers: [\n        next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: process.env.GOOGLE_CLIENT_ID || \"\",\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET || \"\",\n            authorization: {\n                params: {\n                    prompt: \"consent\",\n                    access_type: \"offline\",\n                    response_type: \"code\"\n                }\n            }\n        })\n    ],\n    pages: {\n        signIn: \"/\",\n        error: \"/auth/error\"\n    },\n    callbacks: {\n        async signIn ({ user, account, profile }) {\n            try {\n                console.log(\"\\uD83D\\uDD10 SignIn callback triggered:\", {\n                    user: user.email,\n                    provider: account?.provider,\n                    profile: profile?.name\n                });\n                if (account?.provider === \"google\") {\n                    console.log(\"✅ Google OAuth sign-in successful for:\", user.email);\n                    // For now, we'll skip database operations and just allow sign-in\n                    // TODO: Re-enable database operations once connection is fixed\n                    return true;\n                }\n                return true;\n            } catch (error) {\n                console.error(\"❌ Error during sign in:\", error);\n                return false;\n            }\n        },\n        async jwt ({ token: token1, user, account, profile }) {\n            console.log(\"\\uD83D\\uDD11 JWT callback triggered:\", {\n                hasUser: !!user,\n                tokenEmail: token1.email,\n                userEmail: user?.email\n            });\n            if (user) {\n                console.log(\"\\uD83D\\uDC64 Setting up JWT token for new user:\", user.email);\n                // For testing, we'll set default values without database lookup\n                token1.id = user.id || \"temp-id\";\n                token1.role = \"CLIENT\"; // Default role\n                token1.status = \"ACTIVE\";\n                token1.language = \"ar\";\n                token1.firstName = user.name?.split(\" \")[0] || \"User\";\n                token1.lastName = user.name?.split(\" \").slice(1).join(\" \") || \"\";\n                token1.avatar = user.image || null;\n                token1.hasCompletedOnboarding = false; // Always false for new users\n                console.log(\"✅ JWT token configured:\", {\n                    id: token1.id,\n                    role: token1.role,\n                    hasCompletedOnboarding: token1.hasCompletedOnboarding\n                });\n            }\n            return token1;\n        },\n        async session ({ session, token: token1 }) {\n            console.log(\"\\uD83D\\uDCCB Session callback triggered:\", {\n                tokenEmail: token1.email,\n                sessionEmail: session.user?.email,\n                hasCompletedOnboarding: token1.hasCompletedOnboarding\n            });\n            if (token1) {\n                session.user.id = token1.id;\n                session.user.role = token1.role;\n                session.user.status = token1.status;\n                session.user.language = token1.language;\n                session.user.firstName = token1.firstName;\n                session.user.lastName = token1.lastName;\n                session.user.avatar = token1.avatar;\n                session.user.hasCompletedOnboarding = token1.hasCompletedOnboarding;\n                console.log(\"✅ Session configured:\", {\n                    id: session.user.id,\n                    role: session.user.role,\n                    hasCompletedOnboarding: session.user.hasCompletedOnboarding\n                });\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Handle role-based redirects after successful authentication\n            try {\n                console.log(\"\\uD83D\\uDD04 NextAuth redirect called with:\", {\n                    url,\n                    baseUrl,\n                    token\n                });\n                // For now, redirect all users to AI onboarding\n                // TODO: Check if user has completed onboarding from database\n                // Redirect ALL new users to AI onboarding (MANDATORY)\n                console.log(\"\\uD83E\\uDD16 Redirecting to AI onboarding for new user\");\n                return `${baseUrl}/ai-onboarding`;\n            } catch (error) {\n                console.error(\"❌ Redirect error:\", error);\n                return `${baseUrl}/?auth=error`;\n            }\n        }\n    },\n    session: {\n        strategy: \"jwt\",\n        maxAge: 30 * 24 * 60 * 60\n    },\n    jwt: {\n        maxAge: 30 * 24 * 60 * 60\n    },\n    events: {\n        async signIn ({ user, account }) {\n            console.log(`🎉 User ${user.email} signed in with ${account?.provider}`);\n        // TODO: Re-enable database operations once connection is fixed\n        },\n        async signOut ({ token: token1 }) {\n            console.log(`👋 User ${token1?.email} signed out`);\n        }\n    },\n    debug: \"development\" === \"development\"\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/auth/[...nextauth].ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/../../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fauth%2F%5B...nextauth%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cauth%5C%5B...nextauth%5D.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();