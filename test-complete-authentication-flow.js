const axios = require('axios');

// Test configuration
const LANDING_PAGE_URL = 'http://localhost:3004';
const API_BASE_URL = 'http://localhost:3001';

console.log('🚀 Starting Freela Syria Authentication Flow Test');
console.log('=' .repeat(60));

async function testLandingPageAccessibility() {
  console.log('\n📄 Testing Landing Page Accessibility...');
  try {
    const response = await axios.get(LANDING_PAGE_URL, { timeout: 10000 });
    console.log('✅ Landing page accessible:', response.status);
    
    // Check if the page contains expected elements
    const html = response.data;
    const hasAuthButton = html.includes('Google') || html.includes('تسجيل الدخول');
    const hasArabicContent = html.includes('فريلا') || html.includes('سوريا');
    
    console.log('✅ Contains authentication elements:', hasAuthButton);
    console.log('✅ Contains Arabic content:', hasArabicContent);
    
    return true;
  } catch (error) {
    console.error('❌ Landing page test failed:', error.message);
    return false;
  }
}

async function testAPIEndpoints() {
  console.log('\n🔌 Testing API Endpoints...');
  
  const endpoints = [
    { path: '/health', name: 'Basic Health Check' },
    { path: '/api/v1/health', name: 'API Health Check' },
    { path: '/api/v1/docs', name: 'API Documentation' }
  ];

  let allPassed = true;

  for (const endpoint of endpoints) {
    try {
      const response = await axios.get(`${API_BASE_URL}${endpoint.path}`, { timeout: 5000 });
      console.log(`✅ ${endpoint.name}: ${response.status}`);
      
      if (endpoint.path === '/api/v1/health') {
        const data = response.data;
        console.log(`   Database: ${data.database}`);
        console.log(`   Supabase: ${data.supabase?.connection}`);
      }
    } catch (error) {
      console.error(`❌ ${endpoint.name} failed:`, error.message);
      allPassed = false;
    }
  }

  return allPassed;
}

async function testAuthenticationEndpoints() {
  console.log('\n🔐 Testing Authentication Endpoints...');
  
  try {
    // Test NextAuth configuration endpoint
    const authConfigResponse = await axios.get(`${LANDING_PAGE_URL}/api/auth/providers`, { timeout: 5000 });
    console.log('✅ NextAuth providers endpoint accessible:', authConfigResponse.status);
    
    const providers = authConfigResponse.data;
    const hasGoogleProvider = providers.google !== undefined;
    console.log('✅ Google OAuth provider configured:', hasGoogleProvider);
    
    if (hasGoogleProvider) {
      console.log('   Google OAuth Client ID configured:', !!providers.google.clientId);
    }

    return true;
  } catch (error) {
    console.error('❌ Authentication endpoints test failed:', error.message);
    return false;
  }
}

async function testAIOnboardingEndpoints() {
  console.log('\n🤖 Testing AI Onboarding Endpoints...');
  
  try {
    // Test AI conversation start endpoint (should require authentication)
    const aiStartResponse = await axios.post(`${LANDING_PAGE_URL}/api/ai/conversation/start`, {
      userRole: 'CLIENT',
      language: 'ar',
      sessionType: 'onboarding'
    }, { 
      timeout: 5000,
      validateStatus: function (status) {
        return status < 500; // Accept 4xx errors (expected for unauthenticated requests)
      }
    });
    
    console.log('✅ AI conversation endpoint accessible:', aiStartResponse.status);
    
    if (aiStartResponse.status === 401) {
      console.log('✅ Properly requires authentication (401)');
    } else if (aiStartResponse.status === 200) {
      console.log('✅ AI endpoint working (authenticated or test mode)');
    }

    return true;
  } catch (error) {
    console.error('❌ AI onboarding endpoints test failed:', error.message);
    return false;
  }
}

async function testDatabaseConnectivity() {
  console.log('\n🗄️ Testing Database Connectivity...');
  
  try {
    // Test API database endpoint
    const dbTestResponse = await axios.post(`${API_BASE_URL}/api/v1/auth/test`, {
      email: '<EMAIL>'
    }, { timeout: 10000 });
    
    console.log('✅ Database test endpoint accessible:', dbTestResponse.status);
    
    const data = dbTestResponse.data;
    console.log('✅ Database operation successful:', data.success);
    console.log('   Message:', data.message);
    
    return true;
  } catch (error) {
    console.error('❌ Database connectivity test failed:', error.message);
    return false;
  }
}

async function testAIIntegration() {
  console.log('\n🧠 Testing AI Integration...');
  
  try {
    // Test AI endpoint
    const aiTestResponse = await axios.post(`${API_BASE_URL}/api/v1/ai/test`, {
      message: 'مرحبا، أريد إنشاء ملف شخصي كخبير',
      userRole: 'EXPERT'
    }, { timeout: 10000 });
    
    console.log('✅ AI test endpoint accessible:', aiTestResponse.status);
    
    const data = aiTestResponse.data;
    console.log('✅ AI response generated:', data.success);
    console.log('   Response preview:', data.response?.substring(0, 50) + '...');
    
    return true;
  } catch (error) {
    console.error('❌ AI integration test failed:', error.message);
    return false;
  }
}

async function runCompleteTest() {
  console.log('🔍 Running Complete Authentication Flow Test...\n');
  
  const results = {
    landingPage: await testLandingPageAccessibility(),
    apiEndpoints: await testAPIEndpoints(),
    authentication: await testAuthenticationEndpoints(),
    aiOnboarding: await testAIOnboardingEndpoints(),
    database: await testDatabaseConnectivity(),
    aiIntegration: await testAIIntegration()
  };

  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅ PASS' : '❌ FAIL';
    const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
    console.log(`${status} ${testName}`);
  });

  const allPassed = Object.values(results).every(result => result);
  
  console.log('\n' + '='.repeat(60));
  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED! Authentication flow is ready for testing.');
    console.log('\n📋 Next Steps:');
    console.log('1. Open http://localhost:3004 in your browser');
    console.log('2. Click on the Google sign-in button');
    console.log('3. Complete the OAuth flow');
    console.log('4. Verify redirect to AI onboarding');
    console.log('5. Test the complete user journey');
  } else {
    console.log('⚠️ Some tests failed. Please review the issues above.');
  }
  console.log('='.repeat(60));

  return allPassed;
}

// Run the complete test
runCompleteTest().catch(console.error);
