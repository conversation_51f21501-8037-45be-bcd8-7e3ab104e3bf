const { PrismaClient } = require('@prisma/client');
require('dotenv').config({ path: './apps/api/.env' });

console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');
console.log('DATABASE_URL preview:', process.env.DATABASE_URL ? process.env.DATABASE_URL.substring(0, 80) + '...' : 'N/A');

const prisma = new PrismaClient({
  log: ['query', 'error', 'warn'],
  errorFormat: 'pretty',
});

async function testConnection() {
  const timeout = setTimeout(() => {
    console.log('⏰ Connection test timed out after 10 seconds');
    process.exit(1);
  }, 10000);

  try {
    console.log('🔄 Testing database connection...');

    // Test connection with timeout
    await Promise.race([
      prisma.$connect(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Connection timeout')), 8000)
      )
    ]);

    console.log('✅ Database connected successfully');

    // Test a simple query
    console.log('🔄 Testing simple query...');
    const result = await Promise.race([
      prisma.$queryRaw`SELECT 1 as test`,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Query timeout')), 5000)
      )
    ]);
    console.log('✅ Query test successful:', result);

  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    if (error.code) {
      console.error('Error code:', error.code);
    }
  } finally {
    clearTimeout(timeout);
    try {
      await prisma.$disconnect();
      console.log('🔌 Database disconnected');
    } catch (e) {
      console.log('⚠️ Error during disconnect:', e.message);
    }
  }
}

testConnection();
